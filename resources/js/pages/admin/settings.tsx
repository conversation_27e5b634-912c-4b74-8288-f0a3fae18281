import { Head } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Settings, Globe, Mail, Shield, Database, Palette } from 'lucide-react';

interface SettingsProps {
    settings: {
        site_name: string;
        site_description: string;
        site_url: string;
        contact_email: string;
        admin_email: string;
        maintenance_mode: boolean;
        registration_enabled: boolean;
        email_verification_required: boolean;
        newsletter_enabled: boolean;
        analytics_enabled: boolean;
        seo_enabled: boolean;
        cache_enabled: boolean;
        debug_mode: boolean;
    };
}

export default function AdminSettings({ settings }: SettingsProps) {
    return (
        <AppLayout>
            <Head title="Settings" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
                        <p className="text-muted-foreground">
                            Manage your application settings and configuration
                        </p>
                    </div>
                </div>

                {/* Settings Tabs */}
                <Tabs defaultValue="general" className="space-y-4">
                    <TabsList className="grid w-full grid-cols-5">
                        <TabsTrigger value="general">General</TabsTrigger>
                        <TabsTrigger value="email">Email</TabsTrigger>
                        <TabsTrigger value="security">Security</TabsTrigger>
                        <TabsTrigger value="features">Features</TabsTrigger>
                        <TabsTrigger value="system">System</TabsTrigger>
                    </TabsList>

                    <TabsContent value="general" className="space-y-4">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Globe className="h-5 w-5" />
                                    General Settings
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid gap-4 md:grid-cols-2">
                                    <div className="space-y-2">
                                        <Label htmlFor="site_name">Site Name</Label>
                                        <Input
                                            id="site_name"
                                            defaultValue={settings.site_name}
                                            placeholder="Your Site Name"
                                        />
                                    </div>
                                    <div className="space-y-2">
                                        <Label htmlFor="site_url">Site URL</Label>
                                        <Input
                                            id="site_url"
                                            defaultValue={settings.site_url}
                                            placeholder="https://yoursite.com"
                                        />
                                    </div>
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="site_description">Site Description</Label>
                                    <Textarea
                                        id="site_description"
                                        defaultValue={settings.site_description}
                                        placeholder="Describe your site..."
                                        rows={3}
                                    />
                                </div>
                                <Button>Save General Settings</Button>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="email" className="space-y-4">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Mail className="h-5 w-5" />
                                    Email Settings
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid gap-4 md:grid-cols-2">
                                    <div className="space-y-2">
                                        <Label htmlFor="contact_email">Contact Email</Label>
                                        <Input
                                            id="contact_email"
                                            type="email"
                                            defaultValue={settings.contact_email}
                                            placeholder="<EMAIL>"
                                        />
                                    </div>
                                    <div className="space-y-2">
                                        <Label htmlFor="admin_email">Admin Email</Label>
                                        <Input
                                            id="admin_email"
                                            type="email"
                                            defaultValue={settings.admin_email}
                                            placeholder="<EMAIL>"
                                        />
                                    </div>
                                </div>
                                <Button>Save Email Settings</Button>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="security" className="space-y-4">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Shield className="h-5 w-5" />
                                    Security Settings
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                <div className="flex items-center justify-between">
                                    <div className="space-y-0.5">
                                        <Label>User Registration</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Allow new users to register accounts
                                        </p>
                                    </div>
                                    <Switch defaultChecked={settings.registration_enabled} />
                                </div>
                                <div className="flex items-center justify-between">
                                    <div className="space-y-0.5">
                                        <Label>Email Verification</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Require email verification for new accounts
                                        </p>
                                    </div>
                                    <Switch defaultChecked={settings.email_verification_required} />
                                </div>
                                <div className="flex items-center justify-between">
                                    <div className="space-y-0.5">
                                        <Label>Maintenance Mode</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Put the site in maintenance mode
                                        </p>
                                    </div>
                                    <Switch defaultChecked={settings.maintenance_mode} />
                                </div>
                                <Button>Save Security Settings</Button>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="features" className="space-y-4">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Palette className="h-5 w-5" />
                                    Feature Settings
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                <div className="flex items-center justify-between">
                                    <div className="space-y-0.5">
                                        <Label>Newsletter</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Enable newsletter functionality
                                        </p>
                                    </div>
                                    <Switch defaultChecked={settings.newsletter_enabled} />
                                </div>
                                <div className="flex items-center justify-between">
                                    <div className="space-y-0.5">
                                        <Label>Analytics</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Enable analytics tracking
                                        </p>
                                    </div>
                                    <Switch defaultChecked={settings.analytics_enabled} />
                                </div>
                                <div className="flex items-center justify-between">
                                    <div className="space-y-0.5">
                                        <Label>SEO Features</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Enable SEO optimization features
                                        </p>
                                    </div>
                                    <Switch defaultChecked={settings.seo_enabled} />
                                </div>
                                <Button>Save Feature Settings</Button>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="system" className="space-y-4">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Database className="h-5 w-5" />
                                    System Settings
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                <div className="flex items-center justify-between">
                                    <div className="space-y-0.5">
                                        <Label>Cache</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Enable application caching
                                        </p>
                                    </div>
                                    <Switch defaultChecked={settings.cache_enabled} />
                                </div>
                                <div className="flex items-center justify-between">
                                    <div className="space-y-0.5">
                                        <Label>Debug Mode</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Enable debug mode (development only)
                                        </p>
                                    </div>
                                    <Switch defaultChecked={settings.debug_mode} />
                                </div>
                                <div className="pt-4 space-y-4">
                                    <div className="grid gap-2 md:grid-cols-3">
                                        <Button variant="outline">Clear Cache</Button>
                                        <Button variant="outline">Optimize Database</Button>
                                        <Button variant="outline">Run Maintenance</Button>
                                    </div>
                                </div>
                                <Button>Save System Settings</Button>
                            </CardContent>
                        </Card>
                    </TabsContent>
                </Tabs>
            </div>
        </AppLayout>
    );
}
