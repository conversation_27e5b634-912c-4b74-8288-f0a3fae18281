import { NavFooter } from '@/components/nav-footer';
import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem, type SharedData } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import { BookOpen, LayoutGrid, Settings, Users, Building, Calendar, BarChart3 } from 'lucide-react';
import AppLogo from './app-logo';

export function AppSidebar() {
    const { auth } = usePage<SharedData>().props;
    const user = auth.user;

    const getNavItems = (): NavItem[] => {
        const baseItems: NavItem[] = [
            {
                title: 'Dashboard',
                href: '/dashboard',
                icon: LayoutGrid,
            },
        ];

        // Add role-specific navigation items
        if (user.role === 'admin') {
            return [
                ...baseItems,
                {
                    title: 'Admin Panel',
                    href: '/admin/dashboard',
                    icon: Settings,
                },
                {
                    title: 'Users',
                    href: '/admin/users',
                    icon: Users,
                },
                {
                    title: 'Services',
                    href: '/admin/services',
                    icon: Building,
                },
                {
                    title: 'Analytics',
                    href: '/admin/analytics',
                    icon: BarChart3,
                },
            ];
        }

        if (user.role === 'team_member') {
            return [
                ...baseItems,
                {
                    title: 'Consultations',
                    href: '/consultations',
                    icon: Calendar,
                },
                {
                    title: 'Team',
                    href: '/team',
                    icon: Users,
                },
            ];
        }

        // Client role
        return [
            ...baseItems,
            {
                title: 'Book Consultation',
                href: '/book-consultation',
                icon: Calendar,
            },
            {
                title: 'Services',
                href: '/services',
                icon: Building,
            },
        ];
    };

    const getFooterItems = (): NavItem[] => {
        const baseItems: NavItem[] = [
            {
                title: 'Settings',
                href: '/settings/profile',
                icon: Settings,
            },
        ];

        if (user.role === 'admin') {
            return [
                {
                    title: 'Public Site',
                    href: '/',
                    icon: BookOpen,
                },
                ...baseItems,
            ];
        }

        return [
            {
                title: 'Help',
                href: '/contact',
                icon: BookOpen,
            },
            ...baseItems,
        ];
    };

    return (
        <Sidebar collapsible="icon" variant="inset">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href="/dashboard" prefetch>
                                <AppLogo />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent>
                <NavMain items={getNavItems()} />
            </SidebarContent>

            <SidebarFooter>
                <NavFooter items={getFooterItems()} className="mt-auto" />
                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}
