import { render, screen } from '@testing-library/react';
import { ClientDashboard } from '@/components/dashboard/client-dashboard';
import { type User, type Consultation, type Payment } from '@/types';
import { vi } from 'vitest';

// Mock Inertia Link component
vi.mock('@inertiajs/react', () => ({
    Link: ({ children, href, ...props }: Record<string, unknown>) => (
        <a href={href as string} {...props}>
            {children}
        </a>
    ),
}));

const mockUser: User = {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'client',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
};

const mockStats = {
    total_consultations: 5,
    upcoming_consultations: 2,
    completed_consultations: 3,
    total_spent: 750.00,
    pending_payments: 1,
};

const mockUpcomingConsultations: Consultation[] = [
    {
        id: '1',
        service: { id: '1', title: 'Meta Ads Strategy', slug: 'meta-ads-strategy' },
        status: 'confirmed',
        consultation_date: '2024-01-20T10:00:00Z',
        duration: 60,
        meeting_link: 'https://zoom.us/j/123456789',
        created_at: '2024-01-10T10:00:00Z',
        updated_at: '2024-01-10T10:00:00Z',
    },
    {
        id: '2',
        service: { id: '2', title: 'Analytics Review', slug: 'analytics-review' },
        status: 'confirmed',
        consultation_date: '2024-01-22T14:00:00Z',
        duration: 90,
        meeting_link: 'https://meet.google.com/abc-defg-hij',
        created_at: '2024-01-12T10:00:00Z',
        updated_at: '2024-01-12T10:00:00Z',
    },
];

const mockRecentConsultations: Consultation[] = [
    {
        id: '3',
        service: { id: '3', title: 'Website Audit', slug: 'website-audit' },
        status: 'completed',
        consultation_date: '2024-01-10T10:00:00Z',
        duration: 120,
        created_at: '2024-01-05T10:00:00Z',
        updated_at: '2024-01-10T12:00:00Z',
    },
];

const mockRecentPayments: Payment[] = [
    {
        id: '1',
        consultation: {
            id: '3',
            service: { id: '3', title: 'Website Audit', slug: 'website-audit' },
        },
        amount: 250.00,
        status: 'completed',
        payment_method: 'stripe',
        created_at: '2024-01-10T10:00:00Z',
        updated_at: '2024-01-10T10:00:00Z',
    },
    {
        id: '2',
        consultation: {
            id: '1',
            service: { id: '1', title: 'Meta Ads Strategy', slug: 'meta-ads-strategy' },
        },
        amount: 150.00,
        status: 'pending',
        payment_method: 'stripe',
        created_at: '2024-01-15T10:00:00Z',
        updated_at: '2024-01-15T10:00:00Z',
    },
];

describe('ClientDashboard', () => {
    it('renders client dashboard with stats', () => {
        render(
            <ClientDashboard
                user={mockUser}
                stats={mockStats}
                upcomingConsultations={mockUpcomingConsultations}
                recentConsultations={mockRecentConsultations}
                recentPayments={mockRecentPayments}
            />
        );

        // Check if stats are displayed
        expect(screen.getByText('5')).toBeInTheDocument(); // total_consultations
        expect(screen.getByText('3 completed')).toBeInTheDocument();
        expect(screen.getByText('2')).toBeInTheDocument(); // upcoming_consultations
        expect(screen.getByText('$750.00')).toBeInTheDocument(); // total_spent
        expect(screen.getByText('1')).toBeInTheDocument(); // pending_payments
    });

    it('displays upcoming consultations section when consultations exist', () => {
        render(
            <ClientDashboard
                user={mockUser}
                stats={mockStats}
                upcomingConsultations={mockUpcomingConsultations}
                recentConsultations={mockRecentConsultations}
                recentPayments={mockRecentPayments}
            />
        );

        expect(screen.getByText('Upcoming Consultations')).toBeInTheDocument();
        expect(screen.getByText('Meta Ads Strategy')).toBeInTheDocument();
        expect(screen.getByText('Analytics Review')).toBeInTheDocument();
    });

    it('does not display upcoming consultations section when no consultations', () => {
        render(
            <ClientDashboard
                user={mockUser}
                stats={mockStats}
                upcomingConsultations={[]}
                recentConsultations={mockRecentConsultations}
                recentPayments={mockRecentPayments}
            />
        );

        expect(screen.queryByText('Upcoming Consultations')).not.toBeInTheDocument();
    });

    it('displays meeting links for upcoming consultations', () => {
        render(
            <ClientDashboard
                user={mockUser}
                stats={mockStats}
                upcomingConsultations={mockUpcomingConsultations}
                recentConsultations={mockRecentConsultations}
                recentPayments={mockRecentPayments}
            />
        );

        const joinMeetingLinks = screen.getAllByText('Join Meeting');
        expect(joinMeetingLinks).toHaveLength(2);
        
        expect(joinMeetingLinks[0].closest('a')).toHaveAttribute('href', 'https://zoom.us/j/123456789');
        expect(joinMeetingLinks[1].closest('a')).toHaveAttribute('href', 'https://meet.google.com/abc-defg-hij');
    });

    it('displays recent consultations correctly', () => {
        render(
            <ClientDashboard
                user={mockUser}
                stats={mockStats}
                upcomingConsultations={mockUpcomingConsultations}
                recentConsultations={mockRecentConsultations}
                recentPayments={mockRecentPayments}
            />
        );

        expect(screen.getByText('Recent Consultations')).toBeInTheDocument();
        expect(screen.getByText('Website Audit')).toBeInTheDocument();
    });

    it('displays recent payments correctly', () => {
        render(
            <ClientDashboard
                user={mockUser}
                stats={mockStats}
                upcomingConsultations={mockUpcomingConsultations}
                recentConsultations={mockRecentConsultations}
                recentPayments={mockRecentPayments}
            />
        );

        expect(screen.getByText('Payment History')).toBeInTheDocument();
        expect(screen.getByText('$250.00')).toBeInTheDocument();
        expect(screen.getByText('$150.00')).toBeInTheDocument();
    });

    it('shows correct status badges', () => {
        render(
            <ClientDashboard
                user={mockUser}
                stats={mockStats}
                upcomingConsultations={mockUpcomingConsultations}
                recentConsultations={mockRecentConsultations}
                recentPayments={mockRecentPayments}
            />
        );

        expect(screen.getAllByText('confirmed')).toHaveLength(2);
        expect(screen.getByText('completed')).toBeInTheDocument();
        expect(screen.getByText('pending')).toBeInTheDocument();
    });

    it('renders quick action buttons', () => {
        render(
            <ClientDashboard
                user={mockUser}
                stats={mockStats}
                upcomingConsultations={mockUpcomingConsultations}
                recentConsultations={mockRecentConsultations}
                recentPayments={mockRecentPayments}
            />
        );

        expect(screen.getByText('Book Consultation')).toBeInTheDocument();
        expect(screen.getByText('View Services')).toBeInTheDocument();
        expect(screen.getByText('Update Profile')).toBeInTheDocument();
        expect(screen.getByText('Contact Support')).toBeInTheDocument();
    });

    it('has correct links for quick actions', () => {
        render(
            <ClientDashboard
                user={mockUser}
                stats={mockStats}
                upcomingConsultations={mockUpcomingConsultations}
                recentConsultations={mockRecentConsultations}
                recentPayments={mockRecentPayments}
            />
        );

        const bookConsultationLink = screen.getByText('Book Consultation').closest('a');
        expect(bookConsultationLink).toHaveAttribute('href', '/book-consultation');

        const viewServicesLink = screen.getByText('View Services').closest('a');
        expect(viewServicesLink).toHaveAttribute('href', '/services');

        const updateProfileLink = screen.getByText('Update Profile').closest('a');
        expect(updateProfileLink).toHaveAttribute('href', '/settings/profile');
    });

    it('handles empty consultations gracefully', () => {
        render(
            <ClientDashboard
                user={mockUser}
                stats={mockStats}
                upcomingConsultations={[]}
                recentConsultations={[]}
                recentPayments={mockRecentPayments}
            />
        );

        expect(screen.getByText('No consultations yet')).toBeInTheDocument();
    });

    it('handles empty payments gracefully', () => {
        render(
            <ClientDashboard
                user={mockUser}
                stats={mockStats}
                upcomingConsultations={mockUpcomingConsultations}
                recentConsultations={mockRecentConsultations}
                recentPayments={[]}
            />
        );

        expect(screen.getByText('No payments yet')).toBeInTheDocument();
    });

    it('formats currency correctly', () => {
        render(
            <ClientDashboard
                user={mockUser}
                stats={mockStats}
                upcomingConsultations={mockUpcomingConsultations}
                recentConsultations={mockRecentConsultations}
                recentPayments={mockRecentPayments}
            />
        );

        expect(screen.getByText('$750.00')).toBeInTheDocument();
        expect(screen.getByText('$250.00')).toBeInTheDocument();
        expect(screen.getByText('$150.00')).toBeInTheDocument();
    });

    it('formats dates and times correctly', () => {
        render(
            <ClientDashboard
                user={mockUser}
                stats={mockStats}
                upcomingConsultations={mockUpcomingConsultations}
                recentConsultations={mockRecentConsultations}
                recentPayments={mockRecentPayments}
            />
        );

        // Check for formatted dates (exact format may vary based on locale)
        expect(screen.getByText(/Jan 20, 2024/)).toBeInTheDocument();
        expect(screen.getByText(/Jan 22, 2024/)).toBeInTheDocument();
    });

    it('displays consultation duration correctly', () => {
        render(
            <ClientDashboard
                user={mockUser}
                stats={mockStats}
                upcomingConsultations={mockUpcomingConsultations}
                recentConsultations={mockRecentConsultations}
                recentPayments={mockRecentPayments}
            />
        );

        expect(screen.getByText(/60 minutes/)).toBeInTheDocument();
        expect(screen.getByText(/90 minutes/)).toBeInTheDocument();
        expect(screen.getByText(/120 min/)).toBeInTheDocument();
    });
});
